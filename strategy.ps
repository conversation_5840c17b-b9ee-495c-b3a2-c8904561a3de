//@version=5
strategy("Elliott Wave & Supply/Demand Strategy", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=5)

// === INPUTS ===
// Elliott Wave Settings
wave_lookback = input.int(20, title="Wave Lookback Period", minval=5, maxval=100)
fibonacci_levels = input.bool(true, title="Show Fibonacci Levels")

// Supply & Demand Settings
sd_lookback = input.int(10, title="S&D Zone Lookback", minval=5, maxval=50)
zone_width = input.float(0.5, title="Zone Width %", minval=0.1, maxval=2.0)

// Risk Management
stop_loss_pct = input.float(1.5, title="Stop Loss %", minval=0.5, maxval=5.0)
take_profit_ratio = input.float(3.0, title="Risk/Reward Ratio", minval=1.5, maxval=10.0)

// Confirmation Settings
volume_confirmation = input.bool(true, title="Volume Confirmation")
volume_ma_length = input.int(20, title="Volume MA Length", minval=5, maxval=50)
rsi_filter = input.bool(true, title="RSI Filter")
rsi_length = input.int(14, title="RSI Length", minval=5, maxval=30)

// === ELLIOTT WAVE DETECTION ===
// Simplified Wave Detection using pivot points
pivot_high = ta.pivothigh(high, wave_lookback, wave_lookback)
pivot_low = ta.pivotlow(low, wave_lookback, wave_lookback)

// Simple trend detection
ema_fast = ta.ema(close, 21)
ema_slow = ta.ema(close, 55)

// Wave pattern detection (simplified)
bullish_wave = ema_fast > ema_slow and close > ema_fast
bearish_wave = ema_fast < ema_slow and close < ema_fast
corrective_wave = not bullish_wave and not bearish_wave

current_wave = bullish_wave ? "Bullish Impulse" : bearish_wave ? "Bearish Impulse" : "Corrective"

// === SUPPLY & DEMAND ZONES ===
// Simplified Supply & Demand detection
volume_ma = ta.sma(volume, volume_ma_length)
high_volume = volume > volume_ma * 1.5

// Supply zones (resistance areas)
supply_zone = ta.highest(high, sd_lookback) == high and high_volume
supply_level = supply_zone ? high : na
supply_top = supply_level * (1 + zone_width / 100)
supply_bottom = supply_level * (1 - zone_width / 100)

// Demand zones (support areas)
demand_zone = ta.lowest(low, sd_lookback) == low and high_volume
demand_level = demand_zone ? low : na
demand_top = demand_level * (1 + zone_width / 100)
demand_bottom = demand_level * (1 - zone_width / 100)

// === FIBONACCI RETRACEMENTS ===
// Simplified Fibonacci levels based on recent swing
swing_high = ta.highest(high, wave_lookback * 2)
swing_low = ta.lowest(low, wave_lookback * 2)

// Calculate Fibonacci levels
fib_range = swing_high - swing_low
fib_236 = swing_high - fib_range * 0.236
fib_382 = swing_high - fib_range * 0.382
fib_500 = swing_high - fib_range * 0.500
fib_618 = swing_high - fib_range * 0.618
fib_786 = swing_high - fib_range * 0.786

// === CONFIRMATIONS ===
// Volume confirmation
volume_spike = volume > volume_ma * 1.5

// RSI confirmation
rsi = ta.rsi(close, rsi_length)
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70

// === STRATEGY CONDITIONS ===
// Long Entry Conditions
long_wave_condition = current_wave == "Bullish Impulse"
long_demand_condition = not na(demand_level) and close <= demand_top and close >= demand_bottom
long_fib_condition = close <= fib_618 and close >= fib_786
long_volume_condition = not volume_confirmation or volume_spike
long_rsi_condition = not rsi_filter or rsi_oversold or (rsi > 40 and rsi < 60)

long_entry = long_wave_condition and long_demand_condition and long_volume_condition and long_rsi_condition

// Short Entry Conditions
short_wave_condition = current_wave == "Bearish Impulse"
short_supply_condition = not na(supply_level) and close >= supply_bottom and close <= supply_top
short_fib_condition = close >= fib_236 and close <= fib_382
short_volume_condition = not volume_confirmation or volume_spike
short_rsi_condition = not rsi_filter or rsi_overbought or (rsi > 40 and rsi < 60)

short_entry = short_wave_condition and short_supply_condition and short_volume_condition and short_rsi_condition

// === POSITION MANAGEMENT ===
var float entry_price = na
var float stop_price = na
var float target_price = na

// Long Position Management
if long_entry and strategy.position_size == 0
    entry_price := close
    stop_distance = entry_price * stop_loss_pct / 100
    stop_price := entry_price - stop_distance
    target_price := entry_price + (stop_distance * take_profit_ratio)

    strategy.entry("Long", strategy.long, comment="EW+S&D Long")
    strategy.exit("Long Exit", "Long", stop=stop_price, limit=target_price, comment="Long Exit")

// Short Position Management
if short_entry and strategy.position_size == 0
    entry_price := close
    stop_distance = entry_price * stop_loss_pct / 100
    stop_price := entry_price + stop_distance
    target_price := entry_price - (stop_distance * take_profit_ratio)

    strategy.entry("Short", strategy.short, comment="EW+S&D Short")
    strategy.exit("Short Exit", "Short", stop=stop_price, limit=target_price, comment="Short Exit")

// === PLOTTING ===
// Plot Supply & Demand Zones
supply_top_plot = plot(supply_top, color=color.new(color.red, 80), title="Supply Top", linewidth=2)
supply_bottom_plot = plot(supply_bottom, color=color.new(color.red, 80), title="Supply Bottom", linewidth=2)
demand_top_plot = plot(demand_top, color=color.new(color.green, 80), title="Demand Top", linewidth=2)
demand_bottom_plot = plot(demand_bottom, color=color.new(color.green, 80), title="Demand Bottom", linewidth=2)

// Fill Supply & Demand Zones
fill(supply_top_plot, supply_bottom_plot, color=color.new(color.red, 90), title="Supply Zone")
fill(demand_top_plot, demand_bottom_plot, color=color.new(color.green, 90), title="Demand Zone")

// Plot Fibonacci Levels
plot(fibonacci_levels ? fib_236 : na, color=color.new(color.yellow, 50), title="Fib 23.6%", linewidth=1)
plot(fibonacci_levels ? fib_382 : na, color=color.new(color.orange, 50), title="Fib 38.2%", linewidth=1)
plot(fibonacci_levels ? fib_500 : na, color=color.new(color.blue, 50), title="Fib 50.0%", linewidth=2)
plot(fibonacci_levels ? fib_618 : na, color=color.new(color.purple, 50), title="Fib 61.8%", linewidth=2)
plot(fibonacci_levels ? fib_786 : na, color=color.new(color.gray, 50), title="Fib 78.6%", linewidth=1)

// Plot Entry Signals
plotshape(long_entry, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.normal, title="Long Entry")
plotshape(short_entry, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.normal, title="Short Entry")

// Plot Pivot Points
plotshape(not na(pivot_high), style=shape.circle, location=location.abovebar, color=color.red, size=size.tiny, title="Pivot High")
plotshape(not na(pivot_low), style=shape.circle, location=location.belowbar, color=color.green, size=size.tiny, title="Pivot Low")

// === ALERTS ===
alertcondition(long_entry, title="Elliott Wave Long", message="EW+S&D Strategy: Long entry at demand zone with bullish wave pattern")
alertcondition(short_entry, title="Elliott Wave Short", message="EW+S&D Strategy: Short entry at supply zone with bearish wave pattern")

// === INFORMATION TABLE ===
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 10, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "Elliott Wave & S&D", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "Status", text_color=color.black, bgcolor=color.gray)

    table.cell(info_table, 0, 1, "Wave Pattern", text_color=color.black)
    table.cell(info_table, 1, 1, current_wave, text_color=color.black)

    table.cell(info_table, 0, 2, "Supply Zone", text_color=color.black)
    table.cell(info_table, 1, 2, supply_zone ? "Active" : "None", text_color=supply_zone ? color.red : color.black)

    table.cell(info_table, 0, 3, "Demand Zone", text_color=color.black)
    table.cell(info_table, 1, 3, demand_zone ? "Active" : "None", text_color=demand_zone ? color.green : color.black)

    table.cell(info_table, 0, 4, "RSI", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(math.round(rsi, 1)), text_color=color.black)

    table.cell(info_table, 0, 5, "Volume", text_color=color.black)
    table.cell(info_table, 1, 5, volume_spike ? "High" : "Normal", text_color=volume_spike ? color.orange : color.black)

    table.cell(info_table, 0, 6, "Position", text_color=color.black)
    table.cell(info_table, 1, 6, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "None", text_color=color.black)

    table.cell(info_table, 0, 7, "Entry Price", text_color=color.black)
    table.cell(info_table, 1, 7, strategy.position_size != 0 ? str.tostring(entry_price, "#.####") : "N/A", text_color=color.black)

    table.cell(info_table, 0, 8, "P&L", text_color=color.black)
    table.cell(info_table, 1, 8, str.tostring(math.round(strategy.netprofit, 2)), text_color=strategy.netprofit > 0 ? color.green : strategy.netprofit < 0 ? color.red : color.black)

    table.cell(info_table, 0, 9, "Trades", text_color=color.black)
    table.cell(info_table, 1, 9, str.tostring(strategy.closedtrades), text_color=color.black)

// === WAVE LABELS ===
// Label current wave pattern
if barstate.islast and current_wave != "None"
    label.new(bar_index, high, text=current_wave, style=label.style_label_down, color=current_wave == "Bullish Impulse" ? color.green : current_wave == "Bearish Impulse" ? color.red : color.yellow, textcolor=color.white, size=size.normal)