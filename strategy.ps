//@version=5
strategy("Aggressive Supply & Demand Strategy", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

// === INPUTS ===
// Supply & Demand Settings
sd_lookback = input.int(15, title="S&D Zone Lookback", minval=5, maxval=50)
zone_width = input.float(1.0, title="Zone Width %", minval=0.2, maxval=3.0)
zone_strength = input.int(2, title="Min Zone Touches", minval=1, maxval=5)
fresh_zone_only = input.bool(false, title="Fresh Zones Only")

// Reaction Settings
min_reaction_bars = input.int(3, title="Min Reaction Bars", minval=1, maxval=10)
reaction_strength = input.float(0.5, title="Min Reaction %", minval=0.1, maxval=2.0)

// Risk Management
stop_loss_pct = input.float(1.0, title="Stop Loss %", minval=0.3, maxval=3.0)
take_profit_ratio = input.float(2.0, title="Risk/Reward Ratio", minval=1.0, maxval=5.0)

// Filters
volume_filter = input.bool(false, title="Volume Filter")
volume_ma_length = input.int(20, title="Volume MA Length", minval=5, maxval=50)
trend_filter = input.bool(false, title="Trend Filter")
ema_length = input.int(50, title="Trend EMA", minval=20, maxval=200)

// === SUPPLY & DEMAND ZONE DETECTION ===
// Arrays to store zones
var array<float> supply_zones_top = array.new<float>()
var array<float> supply_zones_bottom = array.new<float>()
var array<int> supply_zones_bar = array.new<int>()
var array<int> supply_zones_touches = array.new<int>()
var array<bool> supply_zones_fresh = array.new<bool>()

var array<float> demand_zones_top = array.new<float>()
var array<float> demand_zones_bottom = array.new<float>()
var array<int> demand_zones_bar = array.new<int>()
var array<int> demand_zones_touches = array.new<int>()
var array<bool> demand_zones_fresh = array.new<bool>()

// Trend filter
trend_ema = ta.ema(close, ema_length)
bullish_trend = not trend_filter or close > trend_ema
bearish_trend = not trend_filter or close < trend_ema

// Volume filter
volume_ma = ta.sma(volume, volume_ma_length)
high_volume = not volume_filter or volume > volume_ma * 1.2

// === SUPPLY ZONE CREATION ===
// Detect strong rejection from highs (Supply zones)
strong_rejection_up = false
if high == ta.highest(high, sd_lookback) and high_volume
    // Check for reaction down
    reaction_bars = 0
    reaction_size = 0.0
    for i = 1 to min_reaction_bars
        if i <= bar_index
            reaction_size := reaction_size + (high[i-1] - close[i])
            if close[i] < high[i-1]
                reaction_bars := reaction_bars + 1

    reaction_percent = (reaction_size / high) * 100
    if reaction_bars >= min_reaction_bars and reaction_percent >= reaction_strength
        strong_rejection_up := true

        // Create supply zone
        zone_top = high * (1 + zone_width / 100)
        zone_bottom = high * (1 - zone_width / 100)

        array.push(supply_zones_top, zone_top)
        array.push(supply_zones_bottom, zone_bottom)
        array.push(supply_zones_bar, bar_index)
        array.push(supply_zones_touches, 1)
        array.push(supply_zones_fresh, true)

// === DEMAND ZONE CREATION ===
// Detect strong bounce from lows (Demand zones)
strong_bounce_up = false
if low == ta.lowest(low, sd_lookback) and high_volume
    // Check for reaction up
    reaction_bars = 0
    reaction_size = 0.0
    for i = 1 to min_reaction_bars
        if i <= bar_index
            reaction_size := reaction_size + (close[i] - low[i-1])
            if close[i] > low[i-1]
                reaction_bars := reaction_bars + 1

    reaction_percent = (reaction_size / low) * 100
    if reaction_bars >= min_reaction_bars and reaction_percent >= reaction_strength
        strong_bounce_up := true

        // Create demand zone
        zone_top = low * (1 + zone_width / 100)
        zone_bottom = low * (1 - zone_width / 100)

        array.push(demand_zones_top, zone_top)
        array.push(demand_zones_bottom, zone_bottom)
        array.push(demand_zones_bar, bar_index)
        array.push(demand_zones_touches, 1)
        array.push(demand_zones_fresh, true)

// === ZONE TESTING & ENTRY LOGIC ===
// Check if price is in any active supply zone
in_supply_zone = false
active_supply_top = 0.0
active_supply_bottom = 0.0
supply_zone_fresh = false

if array.size(supply_zones_top) > 0
    for i = array.size(supply_zones_top) - 1 to 0
        zone_top = array.get(supply_zones_top, i)
        zone_bottom = array.get(supply_zones_bottom, i)
        zone_fresh = array.get(supply_zones_fresh, i)

        if close <= zone_top and close >= zone_bottom
            if not fresh_zone_only or zone_fresh
                in_supply_zone := true
                active_supply_top := zone_top
                active_supply_bottom := zone_bottom
                supply_zone_fresh := zone_fresh
                break

// Check if price is in any active demand zone
in_demand_zone = false
active_demand_top = 0.0
active_demand_bottom = 0.0
demand_zone_fresh = false

if array.size(demand_zones_top) > 0
    for i = array.size(demand_zones_top) - 1 to 0
        zone_top = array.get(demand_zones_top, i)
        zone_bottom = array.get(demand_zones_bottom, i)
        zone_fresh = array.get(demand_zones_fresh, i)

        if close <= zone_top and close >= zone_bottom
            if not fresh_zone_only or zone_fresh
                in_demand_zone := true
                active_demand_top := zone_top
                active_demand_bottom := zone_bottom
                demand_zone_fresh := zone_fresh
                break

// === ENTRY CONDITIONS ===
// Long Entry: Price bouncing from demand zone
long_entry = in_demand_zone and bullish_trend and high_volume and close > open

// Short Entry: Price rejecting from supply zone
short_entry = in_supply_zone and bearish_trend and high_volume and close < open

// === POSITION MANAGEMENT ===
var float entry_price = na
var float stop_price = na
var float target_price = na

// Long Position Management - Stop below demand zone
if long_entry and strategy.position_size == 0
    entry_price := close
    stop_price := active_demand_bottom * (1 - stop_loss_pct / 100)
    stop_distance = entry_price - stop_price
    target_price := entry_price + (stop_distance * take_profit_ratio)

    strategy.entry("Long", strategy.long, comment="S&D Long")
    strategy.exit("Long Exit", "Long", stop=stop_price, limit=target_price, comment="Long Exit")

// Short Position Management - Stop above supply zone
if short_entry and strategy.position_size == 0
    entry_price := close
    stop_price := active_supply_top * (1 + stop_loss_pct / 100)
    stop_distance = stop_price - entry_price
    target_price := entry_price - (stop_distance * take_profit_ratio)

    strategy.entry("Short", strategy.short, comment="S&D Short")
    strategy.exit("Short Exit", "Short", stop=stop_price, limit=target_price, comment="Short Exit")

// === PLOTTING ===
// Plot active zones
plot(in_supply_zone ? active_supply_top : na, color=color.new(color.red, 60), title="Active Supply Top", linewidth=3)
plot(in_supply_zone ? active_supply_bottom : na, color=color.new(color.red, 60), title="Active Supply Bottom", linewidth=3)
plot(in_demand_zone ? active_demand_top : na, color=color.new(color.green, 60), title="Active Demand Top", linewidth=3)
plot(in_demand_zone ? active_demand_bottom : na, color=color.new(color.green, 60), title="Active Demand Bottom", linewidth=3)

// Plot trend EMA
plot(trend_filter ? trend_ema : na, color=color.new(color.blue, 50), title="Trend EMA", linewidth=2)

// Plot zone creation signals
plotshape(strong_rejection_up, style=shape.labeldown, location=location.abovebar, color=color.red, text="SUPPLY", textcolor=color.white, size=size.small)
plotshape(strong_bounce_up, style=shape.labelup, location=location.belowbar, color=color.green, text="DEMAND", textcolor=color.white, size=size.small)

// Plot Entry Signals
plotshape(long_entry, style=shape.triangleup, location=location.belowbar, color=color.lime, size=size.large, title="Long Entry")
plotshape(short_entry, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.large, title="Short Entry")

// Background color for active zones
bgcolor(in_supply_zone ? color.new(color.red, 95) : in_demand_zone ? color.new(color.green, 95) : na, title="Active Zone")

// === ALERTS ===
alertcondition(long_entry, title="S&D Long Entry", message="Supply & Demand: Long entry at demand zone - Price bouncing up!")
alertcondition(short_entry, title="S&D Short Entry", message="Supply & Demand: Short entry at supply zone - Price rejecting down!")
alertcondition(strong_bounce_up, title="New Demand Zone", message="New demand zone created - Strong bounce detected!")
alertcondition(strong_rejection_up, title="New Supply Zone", message="New supply zone created - Strong rejection detected!")

// === INFORMATION TABLE ===
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 9, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "Supply & Demand", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "Status", text_color=color.black, bgcolor=color.gray)

    table.cell(info_table, 0, 1, "In Supply Zone", text_color=color.black)
    table.cell(info_table, 1, 1, in_supply_zone ? "YES" : "NO", text_color=in_supply_zone ? color.red : color.black)

    table.cell(info_table, 0, 2, "In Demand Zone", text_color=color.black)
    table.cell(info_table, 1, 2, in_demand_zone ? "YES" : "NO", text_color=in_demand_zone ? color.green : color.black)

    table.cell(info_table, 0, 3, "Supply Zones", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(array.size(supply_zones_top)), text_color=color.black)

    table.cell(info_table, 0, 4, "Demand Zones", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(array.size(demand_zones_top)), text_color=color.black)

    table.cell(info_table, 0, 5, "Volume", text_color=color.black)
    table.cell(info_table, 1, 5, high_volume ? "HIGH" : "Normal", text_color=high_volume ? color.orange : color.black)

    table.cell(info_table, 0, 6, "Position", text_color=color.black)
    table.cell(info_table, 1, 6, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "None", text_color=color.black)

    table.cell(info_table, 0, 7, "P&L", text_color=color.black)
    table.cell(info_table, 1, 7, str.tostring(math.round(strategy.netprofit, 2)), text_color=strategy.netprofit > 0 ? color.green : strategy.netprofit < 0 ? color.red : color.black)

    table.cell(info_table, 0, 8, "Trades", text_color=color.black)
    table.cell(info_table, 1, 8, str.tostring(strategy.closedtrades), text_color=color.black)