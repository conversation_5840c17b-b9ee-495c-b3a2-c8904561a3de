//@version=5
strategy("Elliott Wave & Supply/Demand Strategy", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=5)

// === INPUTS ===
// Elliott Wave Settings
wave_lookback = input.int(20, title="Wave Lookback Period", minval=5, maxval=100)
wave_deviation = input.float(5.0, title="Wave Deviation %", minval=1.0, maxval=20.0)
fibonacci_levels = input.bool(true, title="Show Fibonacci Levels")

// Supply & Demand Settings
sd_lookback = input.int(10, title="S&D Zone Lookback", minval=5, maxval=50)
sd_strength = input.int(3, title="S&D Zone Strength", minval=1, maxval=10)
zone_width = input.float(0.5, title="Zone Width %", minval=0.1, maxval=2.0)
min_zone_touches = input.int(2, title="Min Zone Touches", minval=1, maxval=5)

// Risk Management
stop_loss_pct = input.float(1.5, title="Stop Loss %", minval=0.5, maxval=5.0)
take_profit_ratio = input.float(3.0, title="Risk/Reward Ratio", minval=1.5, maxval=10.0)
max_risk_per_trade = input.float(2.0, title="Max Risk Per Trade %", minval=0.5, maxval=5.0)

// Confirmation Settings
volume_confirmation = input.bool(true, title="Volume Confirmation")
volume_ma_length = input.int(20, title="Volume MA Length", minval=5, maxval=50)
rsi_filter = input.bool(true, title="RSI Filter")
rsi_length = input.int(14, title="RSI Length", minval=5, maxval=30)

// === ELLIOTT WAVE DETECTION ===
// Pivot High/Low Detection
pivot_high = ta.pivothigh(high, wave_lookback, wave_lookback)
pivot_low = ta.pivotlow(low, wave_lookback, wave_lookback)

// Store pivot points
var array<float> highs = array.new<float>()
var array<int> high_bars = array.new<int>()
var array<float> lows = array.new<float>()
var array<int> low_bars = array.new<int>()

if not na(pivot_high)
    array.push(highs, pivot_high)
    array.push(high_bars, bar_index - wave_lookback)
    if array.size(highs) > 10
        array.shift(highs)
        array.shift(high_bars)

if not na(pivot_low)
    array.push(lows, pivot_low)
    array.push(low_bars, bar_index - wave_lookback)
    if array.size(lows) > 10
        array.shift(lows)
        array.shift(low_bars)

// Elliott Wave Pattern Recognition
get_wave_pattern() =>
    var string pattern = "None"
    if array.size(highs) >= 3 and array.size(lows) >= 3
        last_high = array.get(highs, array.size(highs) - 1)
        prev_high = array.get(highs, array.size(highs) - 2)
        last_low = array.get(lows, array.size(lows) - 1)
        prev_low = array.get(lows, array.size(lows) - 2)

        // Impulse Wave Detection (simplified)
        if last_high > prev_high and last_low > prev_low
            pattern := "Bullish Impulse"
        else if last_high < prev_high and last_low < prev_low
            pattern := "Bearish Impulse"
        else
            pattern := "Corrective"
    pattern

current_wave = get_wave_pattern()

// === SUPPLY & DEMAND ZONES ===
// Function to detect supply zones (resistance)
detect_supply_zone() =>
    var float supply_top = na
    var float supply_bottom = na
    var int supply_touches = 0
    var bool fresh_supply = false

    // Look for areas where price rejected multiple times
    if ta.highest(high, sd_lookback) == high and volume > ta.sma(volume, volume_ma_length) * 1.2
        supply_top := high * (1 + zone_width / 100)
        supply_bottom := high * (1 - zone_width / 100)
        supply_touches := 1
        fresh_supply := true

    [supply_top, supply_bottom, supply_touches, fresh_supply]

// Function to detect demand zones (support)
detect_demand_zone() =>
    var float demand_top = na
    var float demand_bottom = na
    var int demand_touches = 0
    var bool fresh_demand = false

    // Look for areas where price bounced multiple times
    if ta.lowest(low, sd_lookback) == low and volume > ta.sma(volume, volume_ma_length) * 1.2
        demand_top := low * (1 + zone_width / 100)
        demand_bottom := low * (1 - zone_width / 100)
        demand_touches := 1
        fresh_demand := true

    [demand_top, demand_bottom, demand_touches, fresh_demand]

[supply_top, supply_bottom, supply_touches, fresh_supply] = detect_supply_zone()
[demand_top, demand_bottom, demand_touches, fresh_demand] = detect_demand_zone()

// === FIBONACCI RETRACEMENTS ===
// Calculate Fibonacci levels for current swing
var float fib_high = na
var float fib_low = na
var float fib_236 = na
var float fib_382 = na
var float fib_500 = na
var float fib_618 = na
var float fib_786 = na

if not na(pivot_high) and not na(pivot_low)
    if array.size(highs) > 0 and array.size(lows) > 0
        fib_high := array.get(highs, array.size(highs) - 1)
        fib_low := array.get(lows, array.size(lows) - 1)

        if fib_high > fib_low
            fib_range = fib_high - fib_low
            fib_236 := fib_high - fib_range * 0.236
            fib_382 := fib_high - fib_range * 0.382
            fib_500 := fib_high - fib_range * 0.500
            fib_618 := fib_high - fib_range * 0.618
            fib_786 := fib_high - fib_range * 0.786

// === CONFIRMATIONS ===
// Volume confirmation
volume_ma = ta.sma(volume, volume_ma_length)
volume_spike = volume > volume_ma * 1.5

// RSI confirmation
rsi = ta.rsi(close, rsi_length)
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70

// === STRATEGY CONDITIONS ===
// Long Entry Conditions
long_wave_condition = current_wave == "Bullish Impulse" or current_wave == "Corrective"
long_demand_condition = not na(demand_bottom) and close <= demand_top and close >= demand_bottom and fresh_demand
long_fib_condition = not na(fib_618) and close <= fib_618 and close >= fib_786
long_volume_condition = not volume_confirmation or volume_spike
long_rsi_condition = not rsi_filter or rsi_oversold or (rsi > 40 and rsi < 60)

long_entry = long_wave_condition and long_demand_condition and long_volume_condition and long_rsi_condition

// Short Entry Conditions
short_wave_condition = current_wave == "Bearish Impulse" or current_wave == "Corrective"
short_supply_condition = not na(supply_top) and close >= supply_bottom and close <= supply_top and fresh_supply
short_fib_condition = not na(fib_236) and close >= fib_236 and close <= fib_382
short_volume_condition = not volume_confirmation or volume_spike
short_rsi_condition = not rsi_filter or rsi_overbought or (rsi > 40 and rsi < 60)

short_entry = short_wave_condition and short_supply_condition and short_volume_condition and short_rsi_condition

// === POSITION MANAGEMENT ===
var float entry_price = na
var float stop_price = na
var float target_price = na

// Long Position Management
if long_entry and strategy.position_size == 0
    entry_price := close
    stop_distance = entry_price * stop_loss_pct / 100
    stop_price := entry_price - stop_distance
    target_price := entry_price + (stop_distance * take_profit_ratio)

    strategy.entry("Long", strategy.long, comment="EW+S&D Long")
    strategy.exit("Long Exit", "Long", stop=stop_price, limit=target_price, comment="Long Exit")

// Short Position Management
if short_entry and strategy.position_size == 0
    entry_price := close
    stop_distance = entry_price * stop_loss_pct / 100
    stop_price := entry_price + stop_distance
    target_price := entry_price - (stop_distance * take_profit_ratio)

    strategy.entry("Short", strategy.short, comment="EW+S&D Short")
    strategy.exit("Short Exit", "Short", stop=stop_price, limit=target_price, comment="Short Exit")

// === PLOTTING ===
// Plot Supply & Demand Zones
supply_top_plot = plot(supply_top, color=color.new(color.red, 80), title="Supply Top", linewidth=2)
supply_bottom_plot = plot(supply_bottom, color=color.new(color.red, 80), title="Supply Bottom", linewidth=2)
demand_top_plot = plot(demand_top, color=color.new(color.green, 80), title="Demand Top", linewidth=2)
demand_bottom_plot = plot(demand_bottom, color=color.new(color.green, 80), title="Demand Bottom", linewidth=2)

// Fill Supply & Demand Zones
fill(supply_top_plot, supply_bottom_plot, color=color.new(color.red, 90), title="Supply Zone")
fill(demand_top_plot, demand_bottom_plot, color=color.new(color.green, 90), title="Demand Zone")

// Plot Fibonacci Levels
plot(fibonacci_levels and not na(fib_236) ? fib_236 : na, color=color.new(color.yellow, 50), title="Fib 23.6%", linewidth=1)
plot(fibonacci_levels and not na(fib_382) ? fib_382 : na, color=color.new(color.orange, 50), title="Fib 38.2%", linewidth=1)
plot(fibonacci_levels and not na(fib_500) ? fib_500 : na, color=color.new(color.blue, 50), title="Fib 50.0%", linewidth=2)
plot(fibonacci_levels and not na(fib_618) ? fib_618 : na, color=color.new(color.purple, 50), title="Fib 61.8%", linewidth=2)
plot(fibonacci_levels and not na(fib_786) ? fib_786 : na, color=color.new(color.gray, 50), title="Fib 78.6%", linewidth=1)

// Plot Entry Signals
plotshape(long_entry, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.normal, title="Long Entry")
plotshape(short_entry, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.normal, title="Short Entry")

// Plot Pivot Points
plotshape(not na(pivot_high), style=shape.circle, location=location.abovebar, color=color.red, size=size.tiny, title="Pivot High")
plotshape(not na(pivot_low), style=shape.circle, location=location.belowbar, color=color.green, size=size.tiny, title="Pivot Low")

// === ALERTS ===
alertcondition(long_entry, title="Elliott Wave Long", message="EW+S&D Strategy: Long entry at demand zone with bullish wave pattern")
alertcondition(short_entry, title="Elliott Wave Short", message="EW+S&D Strategy: Short entry at supply zone with bearish wave pattern")

// === INFORMATION TABLE ===
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 10, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "Elliott Wave & S&D", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "Status", text_color=color.black, bgcolor=color.gray)

    table.cell(info_table, 0, 1, "Wave Pattern", text_color=color.black)
    table.cell(info_table, 1, 1, current_wave, text_color=color.black)

    table.cell(info_table, 0, 2, "Supply Zone", text_color=color.black)
    table.cell(info_table, 1, 2, not na(supply_top) ? "Active" : "None", text_color=not na(supply_top) ? color.red : color.black)

    table.cell(info_table, 0, 3, "Demand Zone", text_color=color.black)
    table.cell(info_table, 1, 3, not na(demand_top) ? "Active" : "None", text_color=not na(demand_top) ? color.green : color.black)

    table.cell(info_table, 0, 4, "RSI", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(math.round(rsi, 1)), text_color=color.black)

    table.cell(info_table, 0, 5, "Volume", text_color=color.black)
    table.cell(info_table, 1, 5, volume_spike ? "High" : "Normal", text_color=volume_spike ? color.orange : color.black)

    table.cell(info_table, 0, 6, "Position", text_color=color.black)
    table.cell(info_table, 1, 6, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "None", text_color=color.black)

    table.cell(info_table, 0, 7, "Entry Price", text_color=color.black)
    table.cell(info_table, 1, 7, strategy.position_size != 0 ? str.tostring(entry_price, "#.####") : "N/A", text_color=color.black)

    table.cell(info_table, 0, 8, "P&L", text_color=color.black)
    table.cell(info_table, 1, 8, str.tostring(math.round(strategy.netprofit, 2)), text_color=strategy.netprofit > 0 ? color.green : strategy.netprofit < 0 ? color.red : color.black)

    table.cell(info_table, 0, 9, "Win Rate", text_color=color.black)
    win_rate = strategy.closed_trades > 0 ? (strategy.wintrades / strategy.closed_trades) * 100 : 0
    table.cell(info_table, 1, 9, str.tostring(math.round(win_rate, 1)) + "%", text_color=win_rate > 50 ? color.green : color.red)

// === WAVE LABELS ===
// Label current wave pattern
if barstate.islast and current_wave != "None"
    label.new(bar_index, high, text=current_wave, style=label.style_label_down, color=current_wave == "Bullish Impulse" ? color.green : current_wave == "Bearish Impulse" ? color.red : color.yellow, textcolor=color.white, size=size.normal)