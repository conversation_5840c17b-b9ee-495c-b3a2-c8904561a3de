//@version=5
strategy("ICT Supply & Demand + Liquidity", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

// Force 15-minute timeframe
if timeframe.period != "15"
    runtime.error("This strategy only works on 15-minute timeframe!")

// === INPUTS ===
// Supply & Demand Settings
sd_lookback = input.int(20, title="S&D Zone Lookback", minval=10, maxval=50)
zone_width = input.float(0.8, title="Zone Width %", minval=0.2, maxval=2.0)

// Gap Settings
min_gap_size = input.float(0.3, title="Min Gap Size %", minval=0.1, maxval=1.0)
gap_lookback = input.int(10, title="Gap Lookback Bars", minval=5, maxval=20)

// Liquidity Settings
asia_start_hour = input.int(0, title="Asia Session Start (UTC)", minval=0, maxval=23)
asia_end_hour = input.int(8, title="Asia Session End (UTC)", minval=0, maxval=23)
liquidity_lookback = input.int(5, title="Liquidity Lookback Days", minval=1, maxval=10)

// Displacement Settings
displacement_bars = input.int(3, title="Displacement Bars", minval=1, maxval=10)
min_displacement = input.float(0.4, title="Min Displacement %", minval=0.1, maxval=1.0)

// Risk Management
stop_loss_pct = input.float(0.8, title="Stop Loss %", minval=0.3, maxval=2.0)
take_profit_ratio = input.float(2.5, title="Risk/Reward Ratio", minval=1.5, maxval=5.0)

// === ASIA SESSION LIQUIDITY ===
// Detect Asia session
is_asia_session = hour >= asia_start_hour and hour <= asia_end_hour

// Track Asia highs and lows
var float asia_high = na
var float asia_low = na
var int asia_high_bar = na
var int asia_low_bar = na

// Update Asia session levels
if is_asia_session
    if na(asia_high) or high > asia_high
        asia_high := high
        asia_high_bar := bar_index
    if na(asia_low) or low < asia_low
        asia_low := low
        asia_low_bar := bar_index
else if not is_asia_session[1] and is_asia_session[2]
    // Reset for new Asia session
    asia_high := na
    asia_low := na

// === GAP DETECTION ===
// Detect gaps between sessions
gap_up = open > close[1] * (1 + min_gap_size / 100)
gap_down = open < close[1] * (1 - min_gap_size / 100)
gap_fill_up = gap_up[1] and low <= close[1]
gap_fill_down = gap_down[1] and high >= close[1]

// === DISPLACEMENT DETECTION ===
// Calculate displacement (strong directional move)
displacement_up = false
displacement_down = false

if bar_index >= displacement_bars
    start_price = close[displacement_bars]
    current_price = close
    price_change_pct = math.abs(current_price - start_price) / start_price * 100

    if price_change_pct >= min_displacement
        if current_price > start_price
            displacement_up := true
        else
            displacement_down := true

// === ICT SUPPLY & DEMAND ZONES ===
// Order blocks - areas where institutions placed large orders
var array<float> supply_zones = array.new<float>()
var array<float> demand_zones = array.new<float>()
var array<int> supply_zones_bar = array.new<int>()
var array<int> demand_zones_bar = array.new<int>()

// Detect Order Blocks (Supply zones)
// Look for bearish order block after displacement
supply_zone_created = false
if displacement_down and not displacement_down[1]
    // Find the last bullish candle before displacement
    for i = 1 to sd_lookback
        if close[i] > open[i] and close[i+1] < open[i+1]  // Last bull candle before bear
            supply_level = high[i]
            array.push(supply_zones, supply_level)
            array.push(supply_zones_bar, bar_index - i)
            supply_zone_created := true
            break

// Detect Order Blocks (Demand zones)
// Look for bullish order block after displacement
demand_zone_created = false
if displacement_up and not displacement_up[1]
    // Find the last bearish candle before displacement
    for i = 1 to sd_lookback
        if close[i] < open[i] and close[i+1] > open[i+1]  // Last bear candle before bull
            demand_level = low[i]
            array.push(demand_zones, demand_level)
            array.push(demand_zones_bar, bar_index - i)
            demand_zone_created := true
            break

// Clean old zones (keep only recent ones)
if array.size(supply_zones) > 10
    array.shift(supply_zones)
    array.shift(supply_zones_bar)

if array.size(demand_zones) > 10
    array.shift(demand_zones)
    array.shift(demand_zones_bar)

// === LIQUIDITY GRAB DETECTION ===
// Check if price grabbed Asia high/low liquidity
asia_high_grabbed = not na(asia_high) and high >= asia_high and high[1] < asia_high
asia_low_grabbed = not na(asia_low) and low <= asia_low and low[1] > asia_low

// === ENTRY CONDITIONS ===
// Long Entry Conditions:
// 1. Price grabs Asia low liquidity (liquidity grab)
// 2. Price returns to demand zone (order block)
// 3. Gap fill completed (if gap exists)
// 4. Displacement occurred

long_liquidity_grab = asia_low_grabbed
long_in_demand_zone = false
long_gap_condition = not gap_down[1] or gap_fill_down
long_displacement_condition = displacement_up

// Check if price is near any demand zone
if array.size(demand_zones) > 0
    for i = array.size(demand_zones) - 1 to 0
        demand_level = array.get(demand_zones, i)
        zone_top = demand_level * (1 + zone_width / 100)
        zone_bottom = demand_level * (1 - zone_width / 100)

        if close <= zone_top and close >= zone_bottom
            long_in_demand_zone := true
            break

// Short Entry Conditions:
// 1. Price grabs Asia high liquidity (liquidity grab)
// 2. Price returns to supply zone (order block)
// 3. Gap fill completed (if gap exists)
// 4. Displacement occurred

short_liquidity_grab = asia_high_grabbed
short_in_supply_zone = false
short_gap_condition = not gap_up[1] or gap_fill_up
short_displacement_condition = displacement_down

// Check if price is near any supply zone
if array.size(supply_zones) > 0
    for i = array.size(supply_zones) - 1 to 0
        supply_level = array.get(supply_zones, i)
        zone_top = supply_level * (1 + zone_width / 100)
        zone_bottom = supply_level * (1 - zone_width / 100)

        if close <= zone_top and close >= zone_bottom
            short_in_supply_zone := true
            break

// Final entry conditions
long_entry = long_liquidity_grab and long_in_demand_zone and long_gap_condition and long_displacement_condition
short_entry = short_liquidity_grab and short_in_supply_zone and short_gap_condition and short_displacement_condition

// === POSITION MANAGEMENT ===
var float entry_price = na
var float stop_price = na
var float target_price = na

// Long Position Management
if long_entry and strategy.position_size == 0
    entry_price := close
    // Stop below recent swing low or Asia low
    stop_reference = not na(asia_low) ? math.min(asia_low, ta.lowest(low, 10)) : ta.lowest(low, 10)
    stop_price := stop_reference * (1 - stop_loss_pct / 100)
    stop_distance = entry_price - stop_price
    target_price := entry_price + (stop_distance * take_profit_ratio)

    strategy.entry("Long", strategy.long, comment="ICT Long")
    strategy.exit("Long Exit", "Long", stop=stop_price, limit=target_price, comment="Long Exit")

// Short Position Management
if short_entry and strategy.position_size == 0
    entry_price := close
    // Stop above recent swing high or Asia high
    stop_reference = not na(asia_high) ? math.max(asia_high, ta.highest(high, 10)) : ta.highest(high, 10)
    stop_price := stop_reference * (1 + stop_loss_pct / 100)
    stop_distance = stop_price - entry_price
    target_price := entry_price - (stop_distance * take_profit_ratio)

    strategy.entry("Short", strategy.short, comment="ICT Short")
    strategy.exit("Short Exit", "Short", stop=stop_price, limit=target_price, comment="Short Exit")

// === MINIMAL PLOTTING (ONLY ENTRY/EXIT) ===
// Plot Entry Signals Only
plotshape(long_entry, style=shape.triangleup, location=location.belowbar, color=color.lime, size=size.normal, title="Long Entry")
plotshape(short_entry, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.normal, title="Short Entry")

// === ALERTS ===
alertcondition(long_entry, title="ICT Long Entry", message="ICT Strategy: Long entry - Liquidity grabbed + Order block + Gap filled + Displacement!")
alertcondition(short_entry, title="ICT Short Entry", message="ICT Strategy: Short entry - Liquidity grabbed + Order block + Gap filled + Displacement!")
alertcondition(asia_high_grabbed, title="Asia High Grabbed", message="Asia session high liquidity grabbed - Watch for reversal!")
alertcondition(asia_low_grabbed, title="Asia Low Grabbed", message="Asia session low liquidity grabbed - Watch for reversal!")
alertcondition(gap_up, title="Gap Up Detected", message="Gap up detected - Watch for gap fill!")
alertcondition(gap_down, title="Gap Down Detected", message="Gap down detected - Watch for gap fill!")
alertcondition(displacement_up, title="Bullish Displacement", message="Strong bullish displacement detected!")
alertcondition(displacement_down, title="Bearish Displacement", message="Strong bearish displacement detected!")