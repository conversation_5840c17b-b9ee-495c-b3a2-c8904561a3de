//@version=5
strategy("RSI-MACD-EMA Swing Strategy", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

// === INPUTS ===
// RSI Settings
rsi_length = input.int(14, title="RSI Length", minval=1)
rsi_oversold = input.int(30, title="RSI Oversold Level", minval=1, maxval=50)
rsi_overbought = input.int(70, title="RSI Overbought Level", minval=50, maxval=100)

// MACD Settings
macd_fast = input.int(12, title="MACD Fast Length", minval=1)
macd_slow = input.int(26, title="MACD Slow Length", minval=1)
macd_signal = input.int(9, title="MACD Signal Length", minval=1)

// EMA Settings
ema_short = input.int(20, title="Short EMA", minval=1)
ema_long = input.int(50, title="Long EMA", minval=1)

// Risk Management
stop_loss_pct = input.float(2.0, title="Stop Loss %", minval=0.1, maxval=10.0)
take_profit_pct = input.float(4.0, title="Take Profit %", minval=0.1, maxval=20.0)
risk_reward_ratio = input.float(2.0, title="Risk/Reward Ratio", minval=1.0, maxval=5.0)

// Time Filter
use_time_filter = input.bool(true, title="Use Time Filter")
start_hour = input.int(9, title="Start Hour", minval=0, maxval=23)
end_hour = input.int(16, title="End Hour", minval=0, maxval=23)

// === CALCULATIONS ===
// RSI
rsi = ta.rsi(close, rsi_length)

// MACD
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// EMAs
ema_short_line = ta.ema(close, ema_short)
ema_long_line = ta.ema(close, ema_long)

// Time filter
time_ok = use_time_filter ? (hour >= start_hour and hour <= end_hour) : true

// === PLOTTING ===
plot(ema_short_line, color=color.blue, title="EMA Short", linewidth=2)
plot(ema_long_line, color=color.red, title="EMA Long", linewidth=2)

// Plot RSI levels
hline(rsi_overbought, "RSI Overbought", color=color.red, linestyle=hline.style_dashed)
hline(rsi_oversold, "RSI Oversold", color=color.green, linestyle=hline.style_dashed)

// === STRATEGY CONDITIONS ===
// Long Conditions
long_condition_1 = rsi < rsi_oversold  // RSI oversold
long_condition_2 = macd_line > signal_line  // MACD bullish
long_condition_3 = ema_short_line > ema_long_line  // EMA bullish trend
long_condition_4 = close > ema_short_line  // Price above short EMA
long_condition_5 = histogram > histogram[1]  // MACD histogram increasing

long_entry = long_condition_1 and long_condition_2 and long_condition_3 and long_condition_4 and long_condition_5 and time_ok

// Short Conditions
short_condition_1 = rsi > rsi_overbought  // RSI overbought
short_condition_2 = macd_line < signal_line  // MACD bearish
short_condition_3 = ema_short_line < ema_long_line  // EMA bearish trend
short_condition_4 = close < ema_short_line  // Price below short EMA
short_condition_5 = histogram < histogram[1]  // MACD histogram decreasing

short_entry = short_condition_1 and short_condition_2 and short_condition_3 and short_condition_4 and short_condition_5 and time_ok

// === POSITION MANAGEMENT ===
// Calculate position size based on risk
var float entry_price = na
var float stop_price = na
var float target_price = na

if long_entry and strategy.position_size == 0
    entry_price := close
    stop_price := entry_price * (1 - stop_loss_pct / 100)
    target_price := entry_price * (1 + take_profit_pct / 100)
    strategy.entry("Long", strategy.long)
    strategy.exit("Long Exit", "Long", stop=stop_price, limit=target_price)

if short_entry and strategy.position_size == 0
    entry_price := close
    stop_price := entry_price * (1 + stop_loss_pct / 100)
    target_price := entry_price * (1 - take_profit_pct / 100)
    strategy.entry("Short", strategy.short)
    strategy.exit("Short Exit", "Short", stop=stop_price, limit=target_price)

// === ADDITIONAL EXIT CONDITIONS ===
// Exit long if RSI becomes overbought and MACD turns bearish
long_exit = strategy.position_size > 0 and rsi > rsi_overbought and macd_line < signal_line
if long_exit
    strategy.close("Long", comment="RSI OB + MACD Bear")

// Exit short if RSI becomes oversold and MACD turns bullish
short_exit = strategy.position_size < 0 and rsi < rsi_oversold and macd_line > signal_line
if short_exit
    strategy.close("Short", comment="RSI OS + MACD Bull")

// === ALERTS ===
alertcondition(long_entry, title="Long Entry Signal", message="RSI-MACD-EMA Strategy: Long Entry Signal")
alertcondition(short_entry, title="Short Entry Signal", message="RSI-MACD-EMA Strategy: Short Entry Signal")
alertcondition(long_exit, title="Long Exit Signal", message="RSI-MACD-EMA Strategy: Long Exit Signal")
alertcondition(short_exit, title="Short Exit Signal", message="RSI-MACD-EMA Strategy: Short Exit Signal")

// === INFORMATION TABLE ===
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1)
    table.cell(info_table, 0, 0, "Strategy Info", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "Values", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 0, 1, "RSI", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(math.round(rsi, 2)), text_color=color.black)
    table.cell(info_table, 0, 2, "MACD", text_color=color.black)
    table.cell(info_table, 1, 2, str.tostring(math.round(macd_line, 4)), text_color=color.black)
    table.cell(info_table, 0, 3, "Signal", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(math.round(signal_line, 4)), text_color=color.black)
    table.cell(info_table, 0, 4, "EMA 20", text_color=color.black)
    table.cell(info_table, 1, 4, str.tostring(math.round(ema_short_line, 2)), text_color=color.black)
    table.cell(info_table, 0, 5, "EMA 50", text_color=color.black)
    table.cell(info_table, 1, 5, str.tostring(math.round(ema_long_line, 2)), text_color=color.black)
    table.cell(info_table, 0, 6, "Position", text_color=color.black)
    table.cell(info_table, 1, 6, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "None", text_color=color.black)
    table.cell(info_table, 0, 7, "P&L", text_color=color.black)
    table.cell(info_table, 1, 7, str.tostring(math.round(strategy.netprofit, 2)), text_color=strategy.netprofit > 0 ? color.green : color.red)